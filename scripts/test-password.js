const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'
});

async function testPassword() {
  try {
    // Get user from database
    const result = await pool.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    
    if (result.rows.length === 0) {
      console.log('❌ User not found');
      return;
    }
    
    const user = result.rows[0];
    console.log('✅ User found:', user.email);
    console.log('📝 Stored hash:', user.password_hash);
    
    // Test password verification
    const password = 'password123';
    const isValid = await bcrypt.compare(password, user.password_hash);
    
    console.log('🔑 Password test:', password);
    console.log('✅ Password valid:', isValid);
    
    // Generate a new hash for comparison
    const newHash = await bcrypt.hash(password, 10);
    console.log('🆕 New hash:', newHash);
    
    const newHashValid = await bcrypt.compare(password, newHash);
    console.log('✅ New hash valid:', newHashValid);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

testPassword();
