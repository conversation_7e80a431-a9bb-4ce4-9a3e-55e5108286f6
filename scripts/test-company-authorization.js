#!/usr/bin/env node

/**
 * Test script to verify company authorization functionality
 * This tests the new company-level authorization API endpoint
 */

// Use global fetch (available in Node.js 18+) or fallback
const fetch = globalThis.fetch;

const BASE_URL = 'http://localhost:3001';

async function testCompanyAuthorization() {
  console.log('🧪 Testing Company Authorization API...\n');

  try {
    // Test 1: Get a company ID from the companies API
    console.log('1. Fetching companies to get test data...');
    const companiesResponse = await fetch(`${BASE_URL}/api/companies?limit=1`);
    
    if (!companiesResponse.ok) {
      throw new Error(`Failed to fetch companies: ${companiesResponse.status}`);
    }
    
    const companies = await companiesResponse.json();
    
    if (!companies || companies.length === 0) {
      throw new Error('No companies found in database');
    }
    
    const testCompany = companies[0];
    console.log(`   ✓ Found test company: ${testCompany.name} (ID: ${testCompany.id})`);
    
    // Test 2: Test company authorization endpoint without authentication
    console.log('\n2. Testing company authorization without authentication...');
    const authResponse = await fetch(`${BASE_URL}/api/companies/${testCompany.id}/authorization`);
    
    if (!authResponse.ok) {
      throw new Error(`Authorization endpoint failed: ${authResponse.status}`);
    }
    
    const authResult = await authResponse.json();
    console.log(`   ✓ Authorization response:`, authResult);
    
    // Verify the response structure
    if (typeof authResult.authorized !== 'boolean') {
      throw new Error('Authorization response missing "authorized" field');
    }
    
    if (!authResult.message) {
      throw new Error('Authorization response missing "message" field');
    }
    
    // Test 3: Verify unauthorized response for unauthenticated user
    if (authResult.authorized) {
      console.log('   ⚠️  Warning: Expected unauthorized response for unauthenticated user');
    } else {
      console.log('   ✓ Correctly returned unauthorized for unauthenticated user');
    }
    
    if (authResult.requiresAuth) {
      console.log('   ✓ Correctly indicates authentication is required');
    }
    
    // Test 4: Test with invalid company ID
    console.log('\n3. Testing with invalid company ID...');
    const invalidResponse = await fetch(`${BASE_URL}/api/companies/invalid-id/authorization`);
    
    if (!invalidResponse.ok) {
      throw new Error(`Invalid company ID test failed: ${invalidResponse.status}`);
    }
    
    const invalidResult = await invalidResponse.json();
    console.log(`   ✓ Invalid company response:`, invalidResult);
    
    if (invalidResult.authorized) {
      throw new Error('Expected unauthorized response for invalid company ID');
    }
    
    console.log('\n✅ All company authorization tests passed!');
    console.log('\n📋 Summary:');
    console.log('   • Company authorization API endpoint is working');
    console.log('   • Properly handles unauthenticated requests');
    console.log('   • Returns correct response structure');
    console.log('   • Handles invalid company IDs gracefully');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

async function testCompanyPage() {
  console.log('\n🧪 Testing Company Page Rendering...\n');
  
  try {
    // Get a company to test with
    const companiesResponse = await fetch(`${BASE_URL}/api/companies?limit=1`);
    const companies = await companiesResponse.json();
    const testCompany = companies[0];
    
    console.log('1. Testing company page loads...');
    const pageResponse = await fetch(`${BASE_URL}/companies/${testCompany.id}`);
    
    if (!pageResponse.ok) {
      throw new Error(`Company page failed to load: ${pageResponse.status}`);
    }
    
    const pageHtml = await pageResponse.text();
    
    // Check if the page contains expected elements
    if (!pageHtml.includes(testCompany.name)) {
      throw new Error('Company page does not contain company name');
    }
    
    console.log(`   ✓ Company page loads successfully for ${testCompany.name}`);
    console.log('   ✓ Page contains company information');
    
    console.log('\n✅ Company page tests passed!');
    
  } catch (error) {
    console.error('\n❌ Company page test failed:', error.message);
    process.exit(1);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Company Authorization Tests\n');
  console.log('Make sure the development server is running on http://localhost:3001\n');
  
  await testCompanyAuthorization();
  await testCompanyPage();
  
  console.log('\n🎉 All tests completed successfully!');
  console.log('\n💡 Next steps:');
  console.log('   • Test the UI in browser to verify verification notices appear correctly');
  console.log('   • Test with different user authentication states');
  console.log('   • Verify that verification restricted blocks no longer repeat per benefit');
}

runTests().catch(console.error);
