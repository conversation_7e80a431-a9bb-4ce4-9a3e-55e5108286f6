#!/usr/bin/env node

/**
 * Test script to verify the verification UI changes
 * This tests that unauthorized users only see verification counts, not the full verification section
 */

const fetch = globalThis.fetch;
const BASE_URL = 'http://localhost:3001';

async function testVerificationChanges() {
  console.log('🧪 Testing Verification UI Changes...\n');

  try {
    // Test 1: Get a company with benefits
    console.log('1. Fetching a company with benefits...');
    const companiesResponse = await fetch(`${BASE_URL}/api/companies?limit=5`);
    
    if (!companiesResponse.ok) {
      throw new Error(`Failed to fetch companies: ${companiesResponse.status}`);
    }
    
    const companies = await companiesResponse.json();
    
    // Find a company with benefits
    let testCompany = null;
    for (const company of companies) {
      const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${company.id}/benefits`);
      if (benefitsResponse.ok) {
        const benefits = await benefitsResponse.json();
        if (benefits && benefits.length > 0) {
          testCompany = company;
          console.log(`   ✓ Found company with benefits: ${company.name} (${benefits.length} benefits)`);
          break;
        }
      }
    }
    
    if (!testCompany) {
      throw new Error('No companies with benefits found');
    }
    
    // Test 2: Check company authorization (should be unauthorized for unauthenticated user)
    console.log('\n2. Testing company authorization...');
    const authResponse = await fetch(`${BASE_URL}/api/companies/${testCompany.id}/authorization`);
    
    if (!authResponse.ok) {
      throw new Error(`Authorization check failed: ${authResponse.status}`);
    }
    
    const authResult = await authResponse.json();
    console.log(`   ✓ Authorization result:`, authResult);
    
    if (authResult.authorized) {
      console.log('   ⚠️  Warning: Expected unauthorized response for unauthenticated user');
      console.log('   ℹ️  This might be because you\'re testing with a user that has company access');
    } else {
      console.log('   ✓ Correctly returned unauthorized for unauthenticated user');
    }
    
    // Test 3: Check that verification counts API works
    console.log('\n3. Testing verification counts API...');
    const benefitsResponse = await fetch(`${BASE_URL}/api/companies/${testCompany.id}/benefits`);
    const benefits = await benefitsResponse.json();
    
    if (benefits && benefits.length > 0) {
      const testBenefit = benefits[0];
      console.log(`   Testing with benefit: ${testBenefit.name}`);
      
      const countsResponse = await fetch(`${BASE_URL}/api/benefit-verifications/${testBenefit.id}`);
      
      if (countsResponse.ok) {
        const counts = await countsResponse.json();
        console.log(`   ✓ Verification counts:`, counts);
      } else {
        console.log(`   ⚠️  Could not fetch verification counts: ${countsResponse.status}`);
      }
    }
    
    console.log('\n✅ Verification changes test completed!');
    console.log('\n📋 Summary of Changes:');
    console.log('   • Company authorization API is working');
    console.log('   • Verification counts API is accessible');
    console.log('   • UI should now show:');
    console.log('     - For unauthorized users: Only verification counts (if any)');
    console.log('     - For authorized users: Full verification section with buttons');
    console.log('     - Company-level verification notice at the end of benefits list');
    console.log('     - No domain information in verification notices');
    
    console.log('\n💡 Manual Testing Steps:');
    console.log('   1. Visit a company page without being signed in');
    console.log('   2. Verify you only see verification counts under benefits (no "Verify this benefit" section)');
    console.log('   3. Verify the company verification notice appears at the end of the benefits list');
    console.log('   4. Verify the notice does NOT show domain information');
    console.log('   5. Sign in with a company email and verify you see the full verification section');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
runTest().catch(console.error);

async function runTest() {
  console.log('🚀 Starting Verification Changes Test\n');
  console.log('Make sure the development server is running on http://localhost:3001\n');
  
  await testVerificationChanges();
  
  console.log('\n🎉 Test completed successfully!');
}
