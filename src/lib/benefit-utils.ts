/**
 * Utility functions for filtering and URL handling
 *
 * Note: This file previously contained URL slug conversion functions for benefits and industries,
 * but both filters now use simple comma-separated values for consistency and simplicity.
 *
 * URL format examples:
 * - Industries: ?industry=Technology,Healthcare
 * - Benefits: ?benefits=Health Insurance,Dental Coverage
 */

// This file is kept for potential future utility functions
// Currently, both industry and benefits filters use simple comma-separated approach:
// - URL encoding: selectedValues.join(',')
// - URL decoding: urlParam.split(',').filter(<PERSON><PERSON>an)
